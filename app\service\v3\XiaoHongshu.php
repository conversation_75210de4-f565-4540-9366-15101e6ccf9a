<?php

namespace app\service\v3;

use app\BaseService;
use DateTime;
use Exception;

/**
 * 小红书订单服务类
 * 用于获取和处理小红书平台的订单数据
 *
 * @package app\service\v3
 */
class XiaoHongshu extends BaseService
{
    // API配置常量
    private const API_URL     = "https://ark.xiaohongshu.com/ark/open_api/v3/common_controller";
    private const API_VERSION = "2.0";

    // 应用凭证配置
    private const APPLICATIONS = [
        'app1' => [
            'app_id'     => 'e840e096a975478984d4',
            'app_secret' => 'c7931a95f48312d7918cde5de2c47b8e'
        ],
        'app2' => [
            'app_id'     => '4787a2382e784ccb9b09',
            'app_secret' => '5a98d1879df35434299a7b7c62e9cb2b'
        ]
    ];

    // 店铺配置
    private const SHOP_CONFIGURATIONS = [
        [
            "platform_type"   => "小红书",
            "platform_name"   => "行吟信息科技（武汉）有限公司(云酒网小红书店）",
            "shop_id"         => "62b98b750d601800010dc853",
            "payment_channel" => '行吟小红书',
            'app'             => self::APPLICATIONS['app1'],
        ],
        [
            "platform_type"   => "小红书",
            "platform_name"   => "木兰朵-小红书",
            "shop_id"         => "650a60e17fa15200013acf16",
            "payment_channel" => '',
            'app'             => self::APPLICATIONS['app1'],
        ],
        [
            "platform_type"   => "小红书",
            "platform_name"   => "小红书-威哥蒸馏所",
            "shop_id"         => "65113b63effd830001ca90e0",
            "payment_channel" => '小红书 - 威哥蒸馏所',
            'app'             => self::APPLICATIONS['app2'],
        ],
        [
            "platform_type"   => "小红书",
            "platform_name"   => "小红书-Brown Brothers布琅兄弟",
            "shop_id"         => "653b599dbffe730001559bd6",
            "payment_channel" => '小红书-Brown Brothers布琅兄弟',
            'app'             => self::APPLICATIONS['app2'],
        ],
    ];

    // 订单状态映射
    private const ORDER_STATUS_MAPPING = [
        1  => "已取消", // 待支付
        9  => "已取消", // 已取消
        2  => "已支付", // 已支付
        3  => "已支付", // 清关中
        4  => "已支付", // 待发货
        5  => "已发货", // 部分发货
        6  => "已发货", // 待收货
        7  => "已完成", // 已完成
        8  => "已完成", // 已关闭
        10 => "已完成", // 换货申请中
    ];

    // 缓存属性
    protected array $accessTokenCache = [];
    protected array $shopInfoCache    = [];

    /**
     * 构造函数
     */
    public function __construct()
    {
        // 初始化逻辑可以在这里添加
    }

    /**
     * 获取店铺访问令牌
     *
     * @param string $shopId 店铺ID
     * @return string 访问令牌
     */
    private function getAccessToken(string $shopId): string
    {
        if (empty($this->accessTokenCache[$shopId])) {
            $shopInfo                        = \Curl::getXiaoHongShuShopInfo(['shop_id' => $shopId]);
            $this->accessTokenCache[$shopId] = $shopInfo['access_token'];
            $this->shopInfoCache[$shopId]    = $shopInfo;
        }

        return $this->accessTokenCache[$shopId];
    }

    /**
     * 调用小红书API
     *
     * @param array $shop 店铺信息
     * @param string $method API方法名
     * @param array $bizParams 业务参数
     * @return array API响应数据
     * @throws Exception 当API调用失败时抛出异常
     */
    private function callApi(array $shop, string $method, array $bizParams): array
    {
        $timestamp   = (string)time();
        $requestBody = array_merge([
            'appId'     => $shop['app']['app_id'],
            'timestamp' => $timestamp,
            'version'   => self::API_VERSION,
            'method'    => $method,
        ], $bizParams);

        $sign                = $this->calculateSignature(
            $method,
            $shop['app']['app_id'],
            $timestamp,
            self::API_VERSION,
            $shop['app']['app_secret']
        );
        $requestBody['sign'] = $sign;

        if (!empty($shop['access_token'])) {
            $requestBody['accessToken'] = $shop['access_token'];
        }

        return $this->executeCurlRequest($requestBody);
    }

    /**
     * 执行CURL请求
     *
     * @param array $requestBody 请求体
     * @return array 响应数据
     * @throws Exception 当请求失败时抛出异常
     */
    private function executeCurlRequest(array $requestBody): array
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL            => self::API_URL,
            CURLOPT_POST           => true,
            CURLOPT_POSTFIELDS     => json_encode($requestBody),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER     => ['Content-Type: application/json;charset=utf-8'],
            CURLOPT_TIMEOUT        => 30, // 添加超时设置
        ]);

        $result   = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new Exception("API请求失败: {$error}");
        }

        curl_close($ch);

        if ($httpCode !== 200) {
            throw new Exception("HTTP请求失败，状态码: {$httpCode}");
        }

        $data = json_decode($result, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("JSON解析失败: " . json_last_error_msg());
        }

        if (($data['error_code'] ?? 0) !== 0) {
            throw new Exception("API错误: " . ($data['error_msg'] ?? '未知错误'));
        }

        return $data['data'] ?? [];
    }

    /**
     * 计算API签名
     *
     * @param string $method API方法名
     * @param string $appId 应用ID
     * @param string $timestamp 时间戳
     * @param string $version API版本
     * @param string $appSecret 应用密钥
     * @return string 签名字符串
     */
    private function calculateSignature(string $method, string $appId, string $timestamp, string $version, string $appSecret): string
    {
        $paramStr = "{$method}?appId={$appId}&timestamp={$timestamp}&version={$version}";
        $signStr  = $paramStr . $appSecret;
        return md5($signStr);
    }

    /**
     * 获取指定店铺的订单数据
     *
     * @param array $shop 店铺信息
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @return array 订单列表
     */
    private function getShopOrders(array $shop, string $startTime, string $endTime): array
    {
        echo "获取{$shop['platform_name']}订单 {$startTime} - {$endTime}" . PHP_EOL;

        // 将时间范围分割为多个时间窗口
        $timeWindows = $this->createTimeWindows($startTime, $endTime, '+1 day -1 second');

        // 获取所有订单号
        $allOrderNumbers = [];
        foreach ($timeWindows as [$start, $end]) {
            $orderNumbers    = $this->fetchOrderNumbers($shop, $start, $end);
            $allOrderNumbers = array_merge($allOrderNumbers, $orderNumbers);
        }

        // 获取订单详情
        $orders = [];
        foreach ($allOrderNumbers as $orderNumber) {
            $order = $this->fetchOrderDetail($shop, $orderNumber);
            if ($order) {
                $orders[] = $order;
            }
        }

        return $orders;
    }

    /**
     * 获取指定时间范围内的订单号列表
     *
     * @param array $shop 店铺信息
     * @param int $startTime 开始时间戳
     * @param int $endTime 结束时间戳
     * @return array 订单号列表
     */
    private function fetchOrderNumbers(array $shop, int $startTime, int $endTime): array
    {
        $orderNumbers = [];
        $pageNumber   = 1;
        $pageSize     = 100;

        do {
            $response = $this->callApi($shop, 'order.getOrderList', [
                'startTime' => $startTime,
                'endTime'   => $endTime,
                'timeType'  => 1, // 按创建时间查询
                'pageNo'    => $pageNumber,
                'pageSize'  => $pageSize,
            ]);

            $orderList = $response['orderList'] ?? [];
            foreach ($orderList as $order) {
                $orderNumbers[] = $order['orderId'];
            }

            $total = $response['total'] ?? 0;
            $pageNumber++;
        } while (count($orderNumbers) < $total);

        return $orderNumbers;
    }

    /**
     * 获取订单详情
     *
     * @param array $shop 店铺信息
     * @param string $orderNumber 订单号
     * @return array|null 订单详情数据
     */
    private function fetchOrderDetail(array $shop, string $orderNumber): ?array
    {
        $response = $this->callApi($shop, 'order.getOrderDetail', [
            'orderId' => $orderNumber,
        ]);

        return $this->transformOrderData($shop, $response);
    }

    /**
     * 将原始订单数据转换为标准化的订单对象
     *
     * @param array $shop 店铺信息
     * @param array $orderData 原始订单数据
     * @return array 标准化的订单数据
     */
    private function transformOrderData(array $shop, array $orderData): array
    {
        // 判断是否有售后（退款）
        $hasRefund = ($orderData['orderAfterSalesStatus'] ?? 0) == 3;

        // 获取订单状态
        $orderStatus = self::ORDER_STATUS_MAPPING[$orderData['orderStatus'] ?? 0] ?? '未知状态';

        // 如果有售后且订单状态为已完成/已关闭/换货申请中，则标记为已退款
        if ($hasRefund && in_array($orderData['orderStatus'], [7, 8, 10])) {
            $orderStatus = '已退款';
        }

        // 计算商家实收金额（分转元）
        $merchantReceiveAmount = round(($orderData['merchantActualReceiveAmount'] ?? 0) / 100.0, 2);

        // 计算商家应收金额（已退款或已取消的订单应收为0）
        $merchantReceivableAmount = in_array($orderStatus, ['已退款', '已取消']) ? 0 : $merchantReceiveAmount;

        return [
            'order_id'            => $orderData['orderId'] ?? '',
            'platform_type'       => $shop['platform_type'],
            'platform_name'       => $shop['platform_name'],
            'main_order_id'       => $orderData['orderId'] ?? '',
            'month'               => $this->formatTimestamp($orderData['createdTime'] ?? null, 'Y-m'),
            'order_status'        => $orderStatus,
            'shipping_time'       => $orderData['deliveryTime'] ?? null,
            'order_amount'        => $merchantReceiveAmount,
            'merchant_receivable' => $merchantReceivableAmount,
            'receivable_amount'   => $merchantReceiveAmount,
            'payment_channel'     => $shop['payment_channel'],
            'created_at'          => $this->formatTimestamp($orderData['createdTime'] ?? null),
        ];
    }

    /**
     * 格式化时间戳
     *
     * @param int|null $timestamp 时间戳（毫秒）
     * @param string $format 格式化字符串
     * @return string|null 格式化后的时间字符串
     */
    private function formatTimestamp(?int $timestamp, string $format = 'Y-m-d H:i:s'): ?string
    {
        if (!$timestamp) {
            return null;
        }

        return date($format, $timestamp / 1000);
    }


    /**
     * 创建时间窗口
     * 将大的时间范围分割为多个小的时间窗口，避免单次请求数据量过大
     *
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @param string $interval 时间间隔
     * @return array 时间窗口数组，每个元素包含[开始时间戳, 结束时间戳]
     */
    public function createTimeWindows(string $startTime, string $endTime, string $interval = '+1 day'): array
    {
        $startDateTime = new DateTime($startTime);
        $endDateTime   = new DateTime($endTime);
        $timeWindows   = [];

        $current = clone $startDateTime;
        while ($current < $endDateTime) {
            $windowEnd = clone $current;
            $windowEnd->modify($interval);

            // 确保窗口结束时间不超过总结束时间
            if ($windowEnd > $endDateTime) {
                $windowEnd = clone $endDateTime;
            }

            $timeWindows[] = [
                $current->getTimestamp(),
                $windowEnd->getTimestamp(),
            ];

            // 移动到下一个窗口，增加1秒避免重复取值
            $current = clone $windowEnd;
            $current->modify('+1 second');
        }

        return $timeWindows;
    }

    /**
     * 获取所有店铺的订单数据
     *
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @return array 所有店铺的订单列表
     */
    public function getOrders(string $startTime, string $endTime): array
    {
        $allOrders = [];

        foreach (self::SHOP_CONFIGURATIONS as $shopConfig) {
            // 为每个店铺获取访问令牌
            $shopConfig['access_token'] = $this->getAccessToken($shopConfig['shop_id']);

            // 获取该店铺的订单并合并到总订单列表
            $shopOrders = $this->getShopOrders($shopConfig, $startTime, $endTime);
            $allOrders  = array_merge($allOrders, $shopOrders);
        }

        return $allOrders;
    }
}




57517	3365	{"items": [{"id": 3365, "nums": 1, "is_add": true, "inventory_type": "sold"}, {"id": 3365, "nums": 1, "is_add": true, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "available"}]}	3	VHS250206001179510324	订单支付	0	1738852506
57530	3365	{"items": [{"id": 3365, "nums": 1, "is_add": true, "inventory_type": "sold"}, {"id": 3365, "nums": 1, "is_add": true, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "available"}]}	3	VHS250207003092645827	订单支付	0	1738904811
57913	3365	{"items": [{"id": 3365, "nums": 1, "is_add": false, "inventory_type": "sold"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": true, "inventory_type": "available"}]}	3	VHS250207003092645827	订单退款	0	1739408437
57968	3365	{"items": [{"id": 3365, "nums": 1, "is_add": true, "inventory_type": "sold"}, {"id": 3365, "nums": 1, "is_add": true, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "available"}]}	3	VHS250214002382139704	订单支付	0	1739462806
58460	3365	{"items": [{"id": 3365, "nums": 1, "is_add": true, "inventory_type": "sold"}, {"id": 3365, "nums": 1, "is_add": true, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "available"}]}	3	VHS250221002118276333	订单支付	0	1740147761
58505	3365	{"items": [{"id": 3365, "nums": 1, "is_add": true, "inventory_type": "sold"}, {"id": 3365, "nums": 1, "is_add": true, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "available"}]}	3	VHS250223001514272177	订单支付	0	1740311539
59405	3365	{"items": [{"id": 3365, "nums": 1, "is_add": false, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "real"}]}	3	VHS250206001179510324	订单发货	0	1741348992
59410	3365	{"items": [{"id": 3365, "nums": 1, "is_add": false, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "real"}]}	3	VHS250223001514272177	订单发货	0	1741349004
59411	3365	{"items": [{"id": 3365, "nums": 1, "is_add": false, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "real"}]}	3	VHS250214002382139704	订单发货	0	1741349006
59412	3365	{"items": [{"id": 3365, "nums": 1, "is_add": false, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "real"}]}	3	VHS250221002118276333	订单发货	0	1741349009
59979	3365	{"items": [{"id": 3365, "nums": 1, "is_add": true, "inventory_type": "sold"}, {"id": 3365, "nums": 1, "is_add": true, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "available"}]}	3	VHS250313001634085324	订单支付	0	1741840196
60012	3365	{"items": [{"id": 3365, "nums": 1, "is_add": false, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "real"}]}	3	VHS250313001634085324	订单发货	0	1741868401
61380	3365	{"items": [{"id": 3365, "nums": 1, "is_add": true, "inventory_type": "sold"}, {"id": 3365, "nums": 1, "is_add": true, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "available"}]}	3	VHS250402001286748854	订单支付	0	1743593475
61433	3365	{"items": [{"id": 3365, "nums": 1, "is_add": false, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "real"}]}	3	VHS250402001286748854	订单发货	0	1743681039
65370	3365	{"items": [{"id": 3365, "nums": 1, "is_add": true, "inventory_type": "sold"}, {"id": 3365, "nums": 1, "is_add": true, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "available"}]}	3	VHS250502012195271361	订单支付	0	1746118383
66244	3365	{"items": [{"id": 3365, "nums": 1, "is_add": false, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "real"}]}	3	VHS250502012195271361	订单发货	0	1747311609
66785	3365	{"items": [{"id": 3365, "nums": 1, "is_add": true, "inventory_type": "sold"}, {"id": 3365, "nums": 1, "is_add": true, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "available"}]}	3	VHS250528012194676025	订单支付	0	1748412720
66868	3365	{"items": [{"id": 3365, "nums": 1, "is_add": false, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "real"}]}	3	VHS250528012194676025	订单发货	0	1748528133
69792	3365	{"items": [{"id": 3365, "nums": 1, "is_add": true, "inventory_type": "sold"}, {"id": 3365, "nums": 1, "is_add": true, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "available"}]}	3	VHS250725007151708749	订单支付	0	1753433199
69864	3365	{"items": [{"id": 3365, "nums": 1, "is_add": true, "inventory_type": "sold"}, {"id": 3365, "nums": 1, "is_add": true, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "available"}]}	3	VHS250726001431249199	订单支付	0	1753539597
69907	3365	{"items": [{"id": 3365, "nums": 1, "is_add": true, "inventory_type": "sold"}, {"id": 3365, "nums": 1, "is_add": true, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "available"}]}	3	VHS250728001514270662	订单支付	0	1753645809
69942	3365	{"items": [{"id": 3365, "nums": 1, "is_add": false, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "real"}]}	3	VHS250728001514270662	订单发货	0	1753707274
70071	3365	{"items": [{"id": 3365, "nums": 1, "is_add": false, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "real"}]}	3	VHS250725007151708749	订单发货	0	1754030271
70075	3365	{"items": [{"id": 3365, "nums": 1, "is_add": false, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "real"}]}	3	VHS250726001431249199	订单发货	0	1754030281
70712	3365	{"items": [{"id": 3365, "nums": 1, "is_add": true, "inventory_type": "sold"}, {"id": 3365, "nums": 1, "is_add": true, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "available"}]}	3	VHS250816012130571882	订单支付	0	1755285241
70883	3365	{"items": [{"id": 3365, "nums": 1, "is_add": false, "inventory_type": "ts"}, {"id": 3365, "nums": 1, "is_add": false, "inventory_type": "real"}]}	3	VHS250816012130571882	订单发货	0	1755850463