<?php

namespace app\service\v3;

use app\BaseService;
use DateTime;
use Exception;

/**
 * 小红书订单服务类
 * 用于获取和处理小红书平台的订单数据
 *
 * @package app\service\v3
 */
class XiaoHongshu extends BaseService
{
    // API配置常量
    private const API_URL     = "https://ark.xiaohongshu.com/ark/open_api/v3/common_controller";
    private const API_VERSION = "2.0";

    // 应用凭证配置
    private const APPLICATIONS = [
        'app1' => [
            'app_id'     => 'e840e096a975478984d4',
            'app_secret' => 'c7931a95f48312d7918cde5de2c47b8e'
        ],
        'app2' => [
            'app_id'     => '4787a2382e784ccb9b09',
            'app_secret' => '5a98d1879df35434299a7b7c62e9cb2b'
        ]
    ];

    // 店铺配置
    private const SHOP_CONFIGURATIONS = [
        [
            "platform_type"   => "小红书",
            "platform_name"   => "行吟信息科技（武汉）有限公司(云酒网小红书店）",
            "shop_id"         => "62b98b750d601800010dc853",
            "payment_channel" => '行吟小红书',
            'app'             => self::APPLICATIONS['app1'],
        ],
        [
            "platform_type"   => "小红书",
            "platform_name"   => "木兰朵-小红书",
            "shop_id"         => "650a60e17fa15200013acf16",
            "payment_channel" => '',
            'app'             => self::APPLICATIONS['app1'],
        ],
        [
            "platform_type"   => "小红书",
            "platform_name"   => "小红书-威哥蒸馏所",
            "shop_id"         => "65113b63effd830001ca90e0",
            "payment_channel" => '小红书 - 威哥蒸馏所',
            'app'             => self::APPLICATIONS['app2'],
        ],
        [
            "platform_type"   => "小红书",
            "platform_name"   => "小红书-Brown Brothers布琅兄弟",
            "shop_id"         => "653b599dbffe730001559bd6",
            "payment_channel" => '小红书-Brown Brothers布琅兄弟',
            'app'             => self::APPLICATIONS['app2'],
        ],
    ];

    // 订单状态映射
    private const ORDER_STATUS_MAPPING = [
        1  => "已取消", // 待支付
        9  => "已取消", // 已取消
        2  => "已支付", // 已支付
        3  => "已支付", // 清关中
        4  => "已支付", // 待发货
        5  => "已发货", // 部分发货
        6  => "已发货", // 待收货
        7  => "已完成", // 已完成
        8  => "已完成", // 已关闭
        10 => "已完成", // 换货申请中
    ];

    // 缓存属性
    protected array $accessTokenCache = [];
    protected array $shopInfoCache    = [];

    /**
     * 构造函数
     */
    public function __construct()
    {
        // 初始化逻辑可以在这里添加
    }

    /**
     * 获取店铺访问令牌
     *
     * @param string $shopId 店铺ID
     * @return string 访问令牌
     */
    private function getAccessToken(string $shopId): string
    {
        if (empty($this->accessTokenCache[$shopId])) {
            $shopInfo                        = \Curl::getXiaoHongShuShopInfo(['shop_id' => $shopId]);
            $this->accessTokenCache[$shopId] = $shopInfo['access_token'];
            $this->shopInfoCache[$shopId]    = $shopInfo;
        }

        return $this->accessTokenCache[$shopId];
    }

    /**
     * 调用小红书API
     *
     * @param array $shop 店铺信息
     * @param string $method API方法名
     * @param array $bizParams 业务参数
     * @return array API响应数据
     * @throws Exception 当API调用失败时抛出异常
     */
    private function callApi(array $shop, string $method, array $bizParams): array
    {
        $timestamp   = (string)time();
        $requestBody = array_merge([
            'appId'     => $shop['app']['app_id'],
            'timestamp' => $timestamp,
            'version'   => self::API_VERSION,
            'method'    => $method,
        ], $bizParams);

        $sign                = $this->calculateSignature(
            $method,
            $shop['app']['app_id'],
            $timestamp,
            self::API_VERSION,
            $shop['app']['app_secret']
        );
        $requestBody['sign'] = $sign;

        if (!empty($shop['access_token'])) {
            $requestBody['accessToken'] = $shop['access_token'];
        }

        return $this->executeCurlRequest($requestBody);
    }

    /**
     * 执行CURL请求
     *
     * @param array $requestBody 请求体
     * @return array 响应数据
     * @throws Exception 当请求失败时抛出异常
     */
    private function executeCurlRequest(array $requestBody): array
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL            => self::API_URL,
            CURLOPT_POST           => true,
            CURLOPT_POSTFIELDS     => json_encode($requestBody),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER     => ['Content-Type: application/json;charset=utf-8'],
            CURLOPT_TIMEOUT        => 30, // 添加超时设置
        ]);

        $result   = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new Exception("API请求失败: {$error}");
        }

        curl_close($ch);

        if ($httpCode !== 200) {
            throw new Exception("HTTP请求失败，状态码: {$httpCode}");
        }

        $data = json_decode($result, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("JSON解析失败: " . json_last_error_msg());
        }

        if (($data['error_code'] ?? 0) !== 0) {
            throw new Exception("API错误: " . ($data['error_msg'] ?? '未知错误'));
        }

        return $data['data'] ?? [];
    }

    /**
     * 计算API签名
     *
     * @param string $method API方法名
     * @param string $appId 应用ID
     * @param string $timestamp 时间戳
     * @param string $version API版本
     * @param string $appSecret 应用密钥
     * @return string 签名字符串
     */
    private function calculateSignature(string $method, string $appId, string $timestamp, string $version, string $appSecret): string
    {
        $paramStr = "{$method}?appId={$appId}&timestamp={$timestamp}&version={$version}";
        $signStr  = $paramStr . $appSecret;
        return md5($signStr);
    }

    /**
     * 获取指定店铺的订单数据
     *
     * @param array $shop 店铺信息
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @return array 订单列表
     */
    private function getShopOrders(array $shop, string $startTime, string $endTime): array
    {
        echo "获取{$shop['platform_name']}订单 {$startTime} - {$endTime}" . PHP_EOL;

        // 将时间范围分割为多个时间窗口
        $timeWindows = $this->createTimeWindows($startTime, $endTime, '+1 day -1 second');

        // 获取所有订单号
        $allOrderNumbers = [];
        foreach ($timeWindows as [$start, $end]) {
            $orderNumbers    = $this->fetchOrderNumbers($shop, $start, $end);
            $allOrderNumbers = array_merge($allOrderNumbers, $orderNumbers);
        }

        // 获取订单详情
        $orders = [];
        foreach ($allOrderNumbers as $orderNumber) {
            $order = $this->fetchOrderDetail($shop, $orderNumber);
            if ($order) {
                $orders[] = $order;
            }
        }

        return $orders;
    }

    /**
     * 获取指定时间范围内的订单号列表
     *
     * @param array $shop 店铺信息
     * @param int $startTime 开始时间戳
     * @param int $endTime 结束时间戳
     * @return array 订单号列表
     */
    private function fetchOrderNumbers(array $shop, int $startTime, int $endTime): array
    {
        $orderNumbers = [];
        $pageNumber   = 1;
        $pageSize     = 100;

        do {
            $response = $this->callApi($shop, 'order.getOrderList', [
                'startTime' => $startTime,
                'endTime'   => $endTime,
                'timeType'  => 1, // 按创建时间查询
                'pageNo'    => $pageNumber,
                'pageSize'  => $pageSize,
            ]);

            $orderList = $response['orderList'] ?? [];
            foreach ($orderList as $order) {
                $orderNumbers[] = $order['orderId'];
            }

            $total = $response['total'] ?? 0;
            $pageNumber++;
        } while (count($orderNumbers) < $total);

        return $orderNumbers;
    }

    /**
     * 获取订单详情
     *
     * @param array $shop 店铺信息
     * @param string $orderNumber 订单号
     * @return array|null 订单详情数据
     */
    private function fetchOrderDetail(array $shop, string $orderNumber): ?array
    {
        $response = $this->callApi($shop, 'order.getOrderDetail', [
            'orderId' => $orderNumber,
        ]);

        return $this->transformOrderData($shop, $response);
    }

    /**
     * 将原始订单数据转换为标准化的订单对象
     * 与Python版本的_convert_to_order_object和_save_to_local_db逻辑保持一致
     *
     * @param array $shop 店铺信息
     * @param array $orderData 原始订单数据
     * @return array 标准化的订单数据
     */
    private function transformOrderData(array $shop, array $orderData): array
    {
        // 获取基础订单信息
        $orderId = $orderData['orderId'] ?? '';
        $orderStatus = $orderData['orderStatus'] ?? 0;
        $afterSalesStatus = $orderData['orderAfterSalesStatus'] ?? 1;

        // 过滤掉不需要的订单状态（待付款）
        if ($orderStatus == 1) {
            return null; // 返回null表示跳过此订单
        }

        // 映射订单状态（与Python逻辑一致）
        $mappedStatus = $this->mapOrderStatus($orderStatus, $afterSalesStatus);
        if ($mappedStatus === null) {
            return null; // 无法映射的状态跳过
        }

        // 计算金额（分转元）
        $paymentAmount = round(($orderData['totalPayAmount'] ?? 0) / 100.0, 2);
        $merchantReceiveAmount = round(($orderData['merchantActualReceiveAmount'] ?? 0) / 100.0, 2);
        $shippingFee = round(($orderData['totalShippingFree'] ?? 0) / 100.0, 2);
        $merchantDiscount = round(($orderData['totalMerchantDiscount'] ?? 0) / 100.0, 2);
        $platformDiscount = round(($orderData['totalRedDiscount'] ?? 0) / 100.0, 2);

        // 判断是否为退款订单
        $isRefund = $afterSalesStatus == 3;
        $refundAmount = $isRefund ? $merchantReceiveAmount : 0;

        // 计算商家应收金额
        if (in_array($mappedStatus, ['已取消', '已退款'])) {
            $merchantReceivableAmount = $merchantReceiveAmount - $refundAmount;
        } else {
            $merchantReceivableAmount = $merchantReceiveAmount;
        }

        // 判断是否已发货
        $deliveryTime = $orderData['deliveryTime'] ?? null;
        $isShipped = $deliveryTime !== null;
        $unshippedAmount = $isShipped ? 0 : $paymentAmount;

        // 判断是否已回款（假设已支付就是已回款）
        $paidTime = $orderData['paidTime'] ?? null;
        $isPaid = $paidTime !== null;
        $receivedAmount = $isPaid ? $paymentAmount : 0;
        $pendingPayment = $isPaid ? 0 : $paymentAmount;

        // 根据店铺ID确定支付渠道
        $paymentChannel = $this->getPaymentChannelByShopId($shop['shop_id']);

        // 时间戳转换（毫秒转秒）
        $createdTime = $this->convertTimestampToSeconds($orderData['createdTime'] ?? null);
        $paidTimeSeconds = $this->convertTimestampToSeconds($paidTime);
        $deliveryTimeSeconds = $this->convertTimestampToSeconds($deliveryTime);
        $finishTimeSeconds = $this->convertTimestampToSeconds($orderData['finishTime'] ?? null);

        return [
            // 基础订单信息
            'order_id'            => $orderId,
            'main_order_id'       => $orderId,
            'platform_type'       => $shop['platform_type'],
            'platform_name'       => $shop['platform_name'],
            'order_status'        => $mappedStatus,

            // 时间信息
            'created_at'          => $this->formatTimestamp($orderData['createdTime'] ?? null),
            'month'               => $this->formatTimestamp($orderData['createdTime'] ?? null, 'Y-m'),
            'shipping_time'       => $deliveryTimeSeconds,
            'end_time'            => $finishTimeSeconds,

            // 金额信息
            'order_amount'        => $merchantReceiveAmount,
            'payment_amount'      => $paymentAmount,
            'merchant_receivable' => $merchantReceivableAmount,
            'receivable_amount'   => $merchantReceiveAmount,
            'refund_amount'       => $refundAmount,
            'shipping_fee'        => $shippingFee,
            'merchant_discount'   => $merchantDiscount,
            'platform_discount'   => $platformDiscount,
            'unshipped_amount'    => $unshippedAmount,
            'received_amount'     => $receivedAmount,
            'pending_payment'     => max(0, $merchantReceivableAmount - $receivedAmount),

            // 渠道信息
            'payment_channel'     => $paymentChannel,

            // ERP相关金额（初始化为0）
            'u8c_029_amount'      => 0,
            'u8c_515_amount'      => 0,
            't_plus_002_amount'   => 0,
            't_plus_008_amount'   => 0,
            'sales_order_total'   => 0,
            'sales_order_date'    => '',
            'payment_date'        => null,

            // 其他信息
            'order_type'          => $orderData['orderType'] ?? null,
            'cancel_status'       => $orderData['cancelStatus'] ?? null,
            'express_no'          => $orderData['expressTrackingNo'] ?? '',
            'express_company'     => $orderData['expressCompanyCode'] ?? '',
            'customer_remark'     => $orderData['customerRemark'] ?? '',
            'seller_remark'       => $orderData['sellerRemark'] ?? '',
            'sku_list'            => $orderData['skuList'] ?? [],
        ];
    }

    /**
     * 映射订单状态（与Python逻辑保持一致）
     *
     * @param int $orderStatus 原始订单状态
     * @param int $afterSalesStatus 售后状态
     * @return string|null 映射后的状态，null表示跳过
     */
    private function mapOrderStatus(int $orderStatus, int $afterSalesStatus): ?string
    {
        if ($orderStatus == 9) { // 已取消
            return '已取消';
        } elseif (in_array($orderStatus, [7, 8, 10])) { // 已完成、已关闭、换货申请中
            if ($afterSalesStatus == 3) { // 有售后
                return '已退款';
            } else {
                return '已完成';
            }
        } elseif (in_array($orderStatus, [5, 6])) { // 部分发货、待收货
            return '已发货';
        } elseif (in_array($orderStatus, [2, 3, 4])) { // 已支付、清关中、待发货
            return '已支付';
        }

        return null; // 无法映射的状态
    }

    /**
     * 根据店铺ID获取支付渠道
     *
     * @param string $shopId 店铺ID
     * @return string 支付渠道
     */
    private function getPaymentChannelByShopId(string $shopId): string
    {
        $channelMap = [
            '62b98b750d601800010dc853' => '行吟小红书',                    // 行吟信息科技（武汉）有限公司
            '65113b63effd830001ca90e0' => '小红书 - 威哥蒸馏所',           // 小红书-威哥蒸馏所
            '653b599dbffe730001559bd6' => '小红书-Brown Brothers布琅兄弟', // 小红书-Brown Brothers布琅兄弟
        ];

        return $channelMap[$shopId] ?? ''; // 其他店铺（如木兰朵-小红书）返回空字符串
    }

    /**
     * 将毫秒时间戳转换为秒时间戳
     *
     * @param int|null $timestamp 毫秒时间戳
     * @return int|null 秒时间戳
     */
    private function convertTimestampToSeconds(?int $timestamp): ?int
    {
        if (!$timestamp) {
            return null;
        }

        return intval($timestamp / 1000);
    }

    /**
     * 格式化时间戳
     *
     * @param int|null $timestamp 时间戳（毫秒）
     * @param string $format 格式化字符串
     * @return string|null 格式化后的时间字符串
     */
    private function formatTimestamp(?int $timestamp, string $format = 'Y-m-d H:i:s'): ?string
    {
        if (!$timestamp) {
            return null;
        }

        return date($format, $timestamp / 1000);
    }


    /**
     * 创建时间窗口
     * 将大的时间范围分割为多个小的时间窗口，避免单次请求数据量过大
     *
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @param string $interval 时间间隔
     * @return array 时间窗口数组，每个元素包含[开始时间戳, 结束时间戳]
     */
    public function createTimeWindows(string $startTime, string $endTime, string $interval = '+1 day'): array
    {
        $startDateTime = new DateTime($startTime);
        $endDateTime   = new DateTime($endTime);
        $timeWindows   = [];

        $current = clone $startDateTime;
        while ($current < $endDateTime) {
            $windowEnd = clone $current;
            $windowEnd->modify($interval);

            // 确保窗口结束时间不超过总结束时间
            if ($windowEnd > $endDateTime) {
                $windowEnd = clone $endDateTime;
            }

            $timeWindows[] = [
                $current->getTimestamp(),
                $windowEnd->getTimestamp(),
            ];

            // 移动到下一个窗口，增加1秒避免重复取值
            $current = clone $windowEnd;
            $current->modify('+1 second');
        }

        return $timeWindows;
    }

    /**
     * 获取所有店铺的订单数据
     *
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @return array 所有店铺的订单列表
     */
    public function getOrders(string $startTime, string $endTime): array
    {
        $allOrders = [];

        foreach (self::SHOP_CONFIGURATIONS as $shopConfig) {
            // 为每个店铺获取访问令牌
            $shopConfig['access_token'] = $this->getAccessToken($shopConfig['shop_id']);

            // 获取该店铺的订单并合并到总订单列表
            $shopOrders = $this->getShopOrders($shopConfig, $startTime, $endTime);
            $allOrders  = array_merge($allOrders, $shopOrders);
        }

        return $allOrders;
    }
}


